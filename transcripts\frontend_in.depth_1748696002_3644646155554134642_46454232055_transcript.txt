Transcription of: frontend_in.depth_1748696002_3644646155554134642_46454232055.mp3
Model used: base
Language detected: en
--------------------------------------------------

React interviews are next level. I got this challenge to make a React component that calculates how long a user is staying on a page for analytics purposes like Google Analytics with code explanation. Here's how I approached it. Firstly, I start a timer when the page loads. For that, I'll use Use Effect with an empty dependency array. This React hook runs only once when the component mounts, perfect for starting the timer. After that, I'll stop the timer when the user leaves the page. For that, I'll use the cleanup function inside the same use effect. It runs when the component unmounts. Now, I'll calculate the total time spent. For that, I'll use the date object to get both start time and end time and subtract them to get the time duration. Finally, send that time to an analytic service or log it. You can either use console.log or make a custom API call. That was my answer. And <PERSON><PERSON> was clearly impressed. But then, they stuck to the same topic and asked more follow-up questions, which I've shared in the caption below.
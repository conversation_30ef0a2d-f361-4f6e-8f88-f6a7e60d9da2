#!/bin/bash

echo "========================================"
echo "Real-time Speech-to-Text Build Script"
echo "========================================"
echo

echo "Installing build dependencies..."
pip install pyinstaller

echo
echo "Building executable with basic models (tiny, base, small)..."
python build_binary.py --models tiny base small

echo
echo "Build complete! Check the 'distribution' folder."
echo "Press any key to continue..."
read -n 1

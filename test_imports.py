#!/usr/bin/env python3
"""
Test script to verify all imports work correctly
"""

try:
    import os
    print("✅ os imported")
    
    import sys
    print("✅ sys imported")
    
    import threading
    print("✅ threading imported")
    
    import time
    print("✅ time imported")
    
    import queue
    print("✅ queue imported")
    
    import numpy as np
    print("✅ numpy imported")
    
    import pyaudio
    print("✅ pyaudio imported")
    
    import whisper
    print("✅ whisper imported")
    
    print("\n🎉 All imports successful!")
    
    # Test PyAudio device enumeration
    print("\n🔍 Testing PyAudio device enumeration...")
    audio = pyaudio.PyAudio()
    device_count = audio.get_device_count()
    print(f"Found {device_count} audio devices")
    
    input_devices = 0
    for i in range(device_count):
        try:
            device_info = audio.get_device_info_by_index(i)
            if device_info['maxInputChannels'] > 0:
                input_devices += 1
                print(f"  Input Device {i}: {device_info['name']}")
        except Exception as e:
            print(f"  Error reading device {i}: {e}")
    
    print(f"\n📱 Found {input_devices} input devices")
    audio.terminate()
    
    print("\n✅ Basic functionality test completed successfully!")
    
except ImportError as e:
    print(f"❌ Import error: {e}")
except Exception as e:
    print(f"❌ Error: {e}")

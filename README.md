# Real-time Speech-to-Text CLI

A Python CLI application that provides real-time speech-to-text transcription using OpenAI Whisper.

## Features

- 🎯 **Automatic Audio Device Selection**: Automatically detects and selects the best available microphone
- 🎙️ **Real-time Processing**: Transcribes speech as you talk with minimal delay
- 🌍 **Multi-language Support**: Supports multiple languages with auto-detection
- 📱 **Device Management**: Lists and manages audio input devices
- ⚡ **Multiple Model Options**: Choose from different Whisper models based on accuracy vs speed needs
- ✨ **T5 Grammar Correction**: Optional sentence-by-sentence grammar improvement using T5 models
- 🧠 **Parallel Processing**: True parallel architecture with up to 5 dedicated threads
- 📝 **Sentence-Level Processing**: Smart sentence boundary detection for optimal T5 improvement
- 💾 **Dual Output**: Separate files for original transcriptions and improved text
- 🔄 **Word Preservation**: T5 improvements preserve all original transcribed words exactly

## Installation

1. **Install Python dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **For T5 Grammar Correction** (optional):
   ```bash
   pip install transformers torch
   ```

   Note: T5 models require additional disk space (~500MB-2GB depending on model size)

3. **Install PyAudio** (if you encounter issues):
   
   **Windows**:
   ```bash
   pip install pyaudio
   ```
   
   **macOS**:
   ```bash
   brew install portaudio
   pip install pyaudio
   ```
   
   **Linux (Ubuntu/Debian)**:
   ```bash
   sudo apt-get install portaudio19-dev python3-pyaudio
   pip install pyaudio
   ```

## Usage

### Basic Usage (Without T5 Grammar Correction)

Start real-time speech recognition with default settings:
```bash
python realtime_speech_to_text.py
```

This provides:
- Real-time transcription using Whisper
- Console output with timestamps
- Final transcription saved to `final_transcribed.txt`

### T5 Grammar Correction Usage

#### Enable T5 Grammar Correction

**Basic T5 usage** (with default base model):
```bash
python realtime_speech_to_text.py --enable-improvement
```

**Specify T5 model size**:
```bash
# Fast processing (smaller model)
python realtime_speech_to_text.py --enable-improvement --t5-model small

# Better quality (larger model)
python realtime_speech_to_text.py --enable-improvement --t5-model base
```

#### T5 Model Options

| T5 Model | Size | Speed | Quality | Memory Usage |
|----------|------|-------|---------|--------------|
| `small`  | ~240MB | Fast | Good | ~1GB RAM |
| `base`   | ~850MB | Medium | Better | ~2GB RAM |

#### What T5 Grammar Correction Provides

When enabled, you get:
- **Sentence-by-sentence processing**: Waits for complete sentences before improvement
- **Grammar and readability enhancement**: Improves sentence structure while preserving meaning
- **Word preservation**: All original transcribed words are kept exactly
- **Dual output**:
  - Console shows both original and improved sentences
  - `final_transcribed.txt` - Original Whisper transcriptions
  - `improved_transcribed.txt` - T5-enhanced sentences
- **Processing metadata**: Timing information for performance monitoring

### Advanced Usage Examples

**Basic transcription only**:
```bash
python realtime_speech_to_text.py --model small --language en
```

**With T5 grammar correction**:
```bash
python realtime_speech_to_text.py --model small --language en --enable-improvement --t5-model base
```

**Save to custom file with T5**:
```bash
python realtime_speech_to_text.py --output session.txt --enable-improvement --t5-model small
```

**High accuracy mode with T5**:
```bash
python realtime_speech_to_text.py --model medium --language en --enable-improvement --t5-model base
```

**List available audio devices**:
```bash
python realtime_speech_to_text.py --list-devices
```

### Output Files

#### Without T5 (Basic Mode)
- `final_transcribed.txt` - Complete session transcription

#### With T5 Enabled
- `final_transcribed.txt` - Original Whisper transcriptions
- `improved_transcribed.txt` - T5-enhanced sentences with metadata
- Custom output file (if `--output` specified) - Real-time transcription log

### Console Output Examples

#### Basic Mode Output
```
[14:30:15] Hello this is a test of the speech recognition system
[14:30:18] It works pretty well for real time transcription
```

#### T5 Mode Output
```
[14:30:15] Hello this is a test of the speech recognition system
[14:30:15] 📝 Sentence Original: Hello this is a test of the speech recognition system
[14:30:15] ✨ Sentence Improved: Hello, this is a test of the speech recognition system.

[14:30:18] It works pretty well for real time transcription
[14:30:18] 📝 Sentence Original: It works pretty well for real time transcription
[14:30:18] ✨ Sentence Improved: It works pretty well for real-time transcription.
```

## Available Models

### Whisper Models

| Model  | Size | Speed | Accuracy | Use Case |
|--------|------|-------|----------|----------|
| tiny   | 39 MB | Fastest | Basic | Quick testing |
| base   | 74 MB | Fast | Good | Default choice |
| small  | 244 MB | Medium | Better | Balanced |
| medium | 769 MB | Slow | High | High accuracy |
| large  | 1550 MB | Slowest | Highest | Best quality |

### T5 Grammar Correction Models

| T5 Model | Size | Download Time | RAM Usage | Processing Speed | Quality |
|----------|------|---------------|-----------|------------------|---------|
| `small`  | ~240MB | ~30 seconds | ~1GB | Fast | Good grammar fixes |
| `base`   | ~850MB | ~2 minutes | ~2GB | Medium | Better grammar & style |

**Model Selection Guidelines:**
- **For fast processing**: Use `--t5-model small`
- **For best quality**: Use `--t5-model base`
- **For low-memory systems**: Use `--t5-model small` or disable T5
- **For high-accuracy needs**: Use `--t5-model base` with Whisper `medium` or `large`

## T5 Grammar Correction Deep Dive

### How It Works

The T5 grammar correction system implements a sophisticated sentence-by-sentence processing workflow:

1. **Sentence Detection**: Monitors Whisper transcription output for sentence boundaries
2. **Accumulation**: Collects text fragments until complete sentences are formed
3. **Processing**: Applies T5 model to improve grammar while preserving all original words
4. **Output**: Provides both original and improved versions

### Key Features

#### Sentence-by-Sentence Processing
- **Smart Boundary Detection**: Uses regex patterns to identify sentence endings (`.`, `!`, `?`)
- **Fragment Accumulation**: Builds complete sentences from multiple transcription chunks
- **Timeout Handling**: Forces completion after 10 seconds to prevent indefinite waiting

#### Word Preservation
- **Exact Preservation**: All original transcribed words are kept exactly as Whisper produced them
- **Grammar Enhancement**: Improves sentence structure, punctuation, and readability
- **Meaning Preservation**: Maintains original intent while enhancing clarity

#### Parallel Processing
- **Non-blocking Architecture**: T5 processing never blocks Whisper transcription
- **Dedicated Threads**: Separate threads for transcription and improvement
- **Queue-based Communication**: Thread-safe operations with overflow protection

### Performance Considerations

#### System Requirements for T5

**Minimum Requirements:**
- RAM: 4GB (for `small` model)
- RAM: 6GB (for `base` model)
- Storage: 1GB free space for model downloads
- CPU: Multi-core recommended for parallel processing

**Optimal Performance:**
- RAM: 8GB+
- SSD storage for faster model loading
- GPU: Optional, can accelerate T5 processing

#### Processing Speed

| Configuration | Transcription Delay | T5 Processing | Total Delay |
|---------------|-------------------|---------------|-------------|
| Whisper only | ~1-2 seconds | N/A | ~1-2 seconds |
| + T5 small | ~1-2 seconds | ~0.5-1 second | ~2-3 seconds |
| + T5 base | ~1-2 seconds | ~1-2 seconds | ~3-4 seconds |

### Troubleshooting T5

#### T5 Not Working
```bash
# Check if transformers is installed
pip install transformers torch

# Test T5 availability
python -c "from transformers import T5ForConditionalGeneration; print('T5 available')"
```

#### Memory Issues
- Use smaller T5 model: `--t5-model small`
- Close other applications
- Increase system virtual memory

#### Slow Performance
- Use faster T5 model: `--t5-model small`
- Use smaller Whisper model: `--model tiny` or `--model base`
- Ensure adequate RAM available

#### Poor Grammar Corrections
- Try different T5 model size
- Check that complete sentences are being formed
- Verify language compatibility (T5 works best with English)

## Supported Languages

The application supports all languages that Whisper supports, including:
- English (en)
- Spanish (es)
- French (fr)
- German (de)
- Italian (it)
- Portuguese (pt)
- Russian (ru)
- Japanese (ja)
- Korean (ko)
- Chinese (zh)
- And many more...

## How It Works

### Basic Processing Pipeline

1. **Device Detection**: Automatically scans and ranks available audio input devices
2. **Audio Capture**: Captures audio in real-time using PyAudio with voice activity detection
3. **Segmentation**: Processes audio in 1-second intervals or at natural word boundaries
4. **Transcription**: Uses Whisper to convert speech to text with parallel processing
5. **Display**: Shows transcribed text in real-time in the CLI
6. **Output**: Saves final transcription to `final_transcribed.txt`

### With T5 Grammar Correction Enabled

1. **Device Detection**: Same as basic mode
2. **Audio Capture**: Same as basic mode with enhanced voice activity detection
3. **Segmentation**: Same as basic mode
4. **Transcription**: Whisper converts speech to text (Thread 1)
5. **Sentence Assembly**: Fragments are accumulated into complete sentences (Thread 2)
6. **Grammar Improvement**: T5 model enhances complete sentences (Thread 3)
7. **Dual Display**: Shows both original and improved text in real-time
8. **Dual Output**:
   - `final_transcribed.txt` - Original Whisper transcriptions
   - `improved_transcribed.txt` - T5-enhanced sentences

### Thread Architecture

| Thread | Purpose | Processing |
|--------|---------|------------|
| Main | Audio capture & VAD | Real-time audio processing |
| Transcription | Whisper processing | Speech-to-text conversion |
| Sentence Assembly | Fragment accumulation | Sentence boundary detection |
| T5 Improvement | Grammar correction | Sentence-level enhancement |
| Output | File writing & display | Console output & file management |

## Troubleshooting

### No Audio Devices Found
- Check that your microphone is connected and working
- Run with `--list-devices` to see available devices
- Try running as administrator (Windows) or with sudo (Linux)

### Poor Audio Quality
- Check microphone settings in your system
- Try a different Whisper model (larger models are more accurate)
- Ensure you're speaking clearly and close to the microphone

### Performance Issues
- Use a smaller Whisper model (tiny or base)
- Close other applications that might be using the microphone
- Ensure your system meets the requirements

### PyAudio Installation Issues
- On Windows: Try installing Visual C++ Build Tools
- On macOS: Install Xcode command line tools
- On Linux: Install development packages for PortAudio

## Controls

- **Start**: Run the application and start speaking
- **Stop**: Press `Ctrl+C` to stop the application
- **View Devices**: Use `--list-devices` flag

## Tips for Best Results

1. **Speak clearly** and at a normal pace
2. **Minimize background noise** for better accuracy
3. **Use a good quality microphone** if possible
4. **Choose the right model** - larger models are more accurate but slower
5. **Specify language** if you know it for better performance

## Examples

### Basic Transcription Examples

**Quick start with English**:
```bash
python realtime_speech_to_text.py --language en
```

**High accuracy mode**:
```bash
python realtime_speech_to_text.py --model large --language en
```

**Fast mode for testing**:
```bash
python realtime_speech_to_text.py --model tiny
```

### T5 Grammar Correction Examples

**Basic T5 with default settings**:
```bash
python realtime_speech_to_text.py --enable-improvement
```

**T5 with specific models**:
```bash
# Fast T5 processing
python realtime_speech_to_text.py --model base --enable-improvement --t5-model small

# High quality T5 processing
python realtime_speech_to_text.py --model medium --enable-improvement --t5-model base
```

**T5 with custom output**:
```bash
python realtime_speech_to_text.py --output my_session.txt --enable-improvement --t5-model base
```

**Production-ready configuration**:
```bash
python realtime_speech_to_text.py --model small --language en --enable-improvement --t5-model base --output session_$(date +%Y%m%d_%H%M%S).txt
```

### Comparison Examples

**Without T5** (faster, basic output):
```bash
python realtime_speech_to_text.py --model base --language en
# Output: Only final_transcribed.txt
```

**With T5** (slower, enhanced output):
```bash
python realtime_speech_to_text.py --model base --language en --enable-improvement --t5-model base
# Output: final_transcribed.txt + improved_transcribed.txt
```

### Performance Testing

**Minimal resource usage**:
```bash
python realtime_speech_to_text.py --model tiny --t5-model small --enable-improvement
```

**Maximum quality**:
```bash
python realtime_speech_to_text.py --model large --t5-model base --enable-improvement --language en
```

## Building Standalone Binary

You can create a standalone executable that doesn't require Python to be installed on the target machine.

### Using PyInstaller

1. **Install PyInstaller**:
   ```bash
   pip install pyinstaller
   ```

2. **Create basic executable**:
   ```bash
   pyinstaller --onefile realtime_speech_to_text.py
   ```

3. **Create optimized executable with icon** (recommended):
   ```bash
   pyinstaller --onefile --noconsole --name "RealTimeSpeechToText" realtime_speech_to_text.py
   ```

4. **Advanced build with hidden imports** (if you encounter import errors):
   ```bash
   pyinstaller --onefile --hidden-import=whisper --hidden-import=pyaudio --hidden-import=numpy --name "RealTimeSpeechToText" realtime_speech_to_text.py
   ```

### Build Output

- The executable will be created in the `dist/` folder
- File size will be approximately 100-200 MB (includes Python runtime)
- The executable is portable and can run on machines without Python

### Cross-Platform Building

- **Windows**: Creates `.exe` file
- **macOS**: Creates Unix executable
- **Linux**: Creates Unix executable

Note: You must build on the target platform (Windows builds require Windows, etc.)

## Whisper Models Storage & Distribution

### Model Storage Locations

Whisper models are automatically downloaded and cached in:

**Windows**:
```
C:\Users\<USER>\.cache\whisper\
```

**macOS**:
```
~/.cache/whisper/
```

**Linux**:
```
~/.cache/whisper/
```

### Model Files

| Model | File Size | Filename |
|-------|-----------|----------|
| tiny | ~39 MB | `tiny.pt` |
| base | ~74 MB | `base.pt` |
| small | ~244 MB | `small.pt` |
| medium | ~769 MB | `medium.pt` |
| large | ~1550 MB | `large-v3.pt` |

### Including Models in Binary Distribution

#### Option 1: Pre-download Models (Recommended)

1. **Download models beforehand**:
   ```bash
   python -c "import whisper; whisper.load_model('base')"
   python -c "import whisper; whisper.load_model('small')"
   ```

2. **Create distribution package**:
   ```bash
   # Create a models directory
   mkdir models

   # Copy models from cache
   # Windows:
   copy "%USERPROFILE%\.cache\whisper\*.pt" models\

   # macOS/Linux:
   cp ~/.cache/whisper/*.pt models/
   ```

3. **Modify the application** to look for local models first (see code modification below)

#### Option 2: Bundle Models with PyInstaller

1. **Create a spec file for PyInstaller**:
   ```bash
   pyinstaller --onefile --name "RealTimeSpeechToText" realtime_speech_to_text.py --specpath .
   ```

2. **Edit the generated `.spec` file** to include models:
   ```python
   # Add to the Analysis section:
   datas=[('models/*.pt', 'whisper_models')]
   ```

3. **Rebuild with the spec file**:
   ```bash
   pyinstaller RealTimeSpeechToText.spec
   ```

#### Automated Build Script (Recommended)

Use the provided build script for easy executable creation:

**Windows**:
```bash
# Simple build with basic models
build.bat

# Or use the Python script directly
python build_binary.py --models tiny base small
```

**macOS/Linux**:
```bash
# Make script executable and run
chmod +x build.sh
./build.sh

# Or use the Python script directly
python build_binary.py --models tiny base small
```

#### Build Script Options

```bash
# Build with specific models
python build_binary.py --models base medium

# Build without bundled models (smaller executable)
python build_binary.py --no-models

# Build with all models (large file size)
python build_binary.py --models tiny base small medium large

# Skip dependency installation
python build_binary.py --skip-deps
```

#### Manual Build Process

If you prefer manual control:

1. **Pre-download models**:
   ```python
   import whisper
   whisper.load_model('base')  # Downloads and caches the model
   ```

2. **Create models directory**:
   ```bash
   mkdir models
   # Copy from cache (Windows)
   copy "%USERPROFILE%\.cache\whisper\*.pt" models\
   # Copy from cache (macOS/Linux)
   cp ~/.cache/whisper/*.pt models/
   ```

3. **Build executable**:
   ```bash
   pyinstaller --onefile --name "RealTimeSpeechToText" realtime_speech_to_text.py
   ```

### Distribution Package Structure

After building, you'll get a `distribution` folder with:

```
distribution/
├── RealTimeSpeechToText.exe    # Main executable
├── models/                     # Whisper models (if included)
│   ├── tiny.pt
│   ├── base.pt
│   └── small.pt
├── REALTIME_README.md          # Documentation
├── README.md                   # Original documentation
└── start.bat                   # Windows launcher script
```

### Deployment

1. **Zip the distribution folder** for easy sharing
2. **Users only need to**:
   - Extract the zip file
   - Run `RealTimeSpeechToText.exe` (Windows) or `./RealTimeSpeechToText` (Unix)
   - No Python installation required!

### Build Size Estimates

| Configuration | Approximate Size |
|---------------|------------------|
| No models bundled | ~100-150 MB |
| With tiny + base | ~200-250 MB |
| With tiny + base + small | ~400-500 MB |
| With all models | ~2-3 GB |

**Recommendation**: Bundle `tiny`, `base`, and `small` models for a good balance of size and functionality.

## Model Management

### Where Models Are Stored

The application looks for models in this order:

1. **Local `models/` directory** (bundled with executable)
2. **`whisper_models/` directory** (alternative location)
3. **Next to executable** (for portable installations)
4. **User cache directory** (standard Whisper location)
5. **Downloads from internet** (fallback)

### Model Download Behavior

- **First run**: Downloads models to cache if not found locally
- **Subsequent runs**: Uses cached models
- **Bundled executable**: Uses included models, no download needed
- **Offline usage**: Works if models are bundled or pre-downloaded

### Managing Model Storage

**Check current model cache**:
```bash
# Windows
dir "%USERPROFILE%\.cache\whisper"

# macOS/Linux
ls ~/.cache/whisper/
```

**Clear model cache** (to save space):
```bash
# Windows
rmdir /s "%USERPROFILE%\.cache\whisper"

# macOS/Linux
rm -rf ~/.cache/whisper/
```

**Pre-download specific models**:
```python
import whisper

# Download models you want
models = ['tiny', 'base', 'small']
for model in models:
    print(f"Downloading {model}...")
    whisper.load_model(model)
    print(f"✅ {model} downloaded")
```

## Quick Reference

### Command Line Options

| Option | Description | Example |
|--------|-------------|---------|
| `--model` | Whisper model size | `--model base` |
| `--language` | Language code | `--language en` |
| `--enable-improvement` | Enable T5 grammar correction | `--enable-improvement` |
| `--t5-model` | T5 model size | `--t5-model base` |
| `--output` | Custom output file | `--output session.txt` |
| `--list-devices` | List audio devices | `--list-devices` |
| `--debug-vad` | Enable VAD debugging | `--debug-vad` |

### Common Commands

```bash
# Basic usage (no T5)
python realtime_speech_to_text.py

# With T5 grammar correction
python realtime_speech_to_text.py --enable-improvement

# High quality setup
python realtime_speech_to_text.py --model medium --enable-improvement --t5-model base --language en

# Fast setup
python realtime_speech_to_text.py --model tiny --enable-improvement --t5-model small

# List audio devices
python realtime_speech_to_text.py --list-devices

# Custom output file
python realtime_speech_to_text.py --output my_transcription.txt --enable-improvement
```

### Output Files

| File | Content | When Created |
|------|---------|--------------|
| `final_transcribed.txt` | Original Whisper transcriptions | Always |
| `improved_transcribed.txt` | T5-enhanced sentences | When `--enable-improvement` used |
| Custom output file | Real-time transcription log | When `--output` specified |

### Performance Guidelines

| Use Case | Recommended Command |
|----------|-------------------|
| **Quick testing** | `python realtime_speech_to_text.py --model tiny` |
| **Daily use** | `python realtime_speech_to_text.py --model base --enable-improvement --t5-model small` |
| **High accuracy** | `python realtime_speech_to_text.py --model medium --enable-improvement --t5-model base --language en` |
| **Low memory** | `python realtime_speech_to_text.py --model tiny --enable-improvement --t5-model small` |
| **Best quality** | `python realtime_speech_to_text.py --model large --enable-improvement --t5-model base --language en` |

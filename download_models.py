#!/usr/bin/env python3
"""
Model downloader for Whisper models.

This script downloads and caches Whisper models for offline use.
Useful for preparing models before building executables or for offline usage.

Usage:
    python download_models.py [models...]
    
Examples:
    python download_models.py base small
    python download_models.py --all
    python download_models.py --recommended
"""

import argparse
import sys
import whisper
from pathlib import Path
import shutil


def download_model(model_name):
    """Download a specific Whisper model."""
    print(f"📥 Downloading {model_name} model...")
    
    try:
        model = whisper.load_model(model_name)
        print(f"✅ {model_name} model downloaded successfully")
        
        # Get model info
        cache_dir = Path.home() / ".cache" / "whisper"
        model_files = list(cache_dir.glob(f"{model_name}*.pt"))
        
        if model_files:
            model_file = model_files[0]
            size_mb = model_file.stat().st_size / (1024 * 1024)
            print(f"   Size: {size_mb:.1f} MB")
            print(f"   Location: {model_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to download {model_name}: {e}")
        return False


def copy_models_to_local():
    """Copy downloaded models to local models directory."""
    cache_dir = Path.home() / ".cache" / "whisper"
    local_dir = Path("models")
    
    if not cache_dir.exists():
        print("⚠️  No models found in cache directory")
        return False
    
    local_dir.mkdir(exist_ok=True)
    
    model_files = list(cache_dir.glob("*.pt"))
    if not model_files:
        print("⚠️  No model files found in cache")
        return False
    
    print(f"📁 Copying {len(model_files)} models to local directory...")
    
    for model_file in model_files:
        dest_file = local_dir / model_file.name
        if not dest_file.exists():
            shutil.copy2(model_file, dest_file)
            print(f"   Copied: {model_file.name}")
        else:
            print(f"   Skipped: {model_file.name} (already exists)")
    
    return True


def list_available_models():
    """List all available Whisper models."""
    models = {
        'tiny': {'size': '39 MB', 'description': 'Fastest, basic accuracy'},
        'base': {'size': '74 MB', 'description': 'Good balance of speed and accuracy'},
        'small': {'size': '244 MB', 'description': 'Better accuracy, slower'},
        'medium': {'size': '769 MB', 'description': 'High accuracy, much slower'},
        'large': {'size': '1550 MB', 'description': 'Highest accuracy, very slow'}
    }
    
    print("\n📋 Available Whisper Models:")
    print("-" * 60)
    
    for model, info in models.items():
        print(f"  {model:8} - {info['size']:>8} - {info['description']}")
    
    print()


def check_downloaded_models():
    """Check which models are already downloaded."""
    cache_dir = Path.home() / ".cache" / "whisper"
    local_dir = Path("models")
    
    print("📊 Model Status:")
    print("-" * 40)
    
    models = ['tiny', 'base', 'small', 'medium', 'large']
    
    for model in models:
        cache_files = list(cache_dir.glob(f"{model}*.pt")) if cache_dir.exists() else []
        local_files = list(local_dir.glob(f"{model}*.pt")) if local_dir.exists() else []
        
        status = []
        if cache_files:
            size_mb = cache_files[0].stat().st_size / (1024 * 1024)
            status.append(f"Cache ({size_mb:.0f}MB)")
        if local_files:
            status.append("Local")
        
        status_str = ", ".join(status) if status else "Not downloaded"
        print(f"  {model:8} - {status_str}")


def main():
    """Main function."""
    parser = argparse.ArgumentParser(
        description="Download Whisper models for offline use",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python download_models.py base small
  python download_models.py --all
  python download_models.py --recommended
  python download_models.py --list
        """
    )
    
    parser.add_argument(
        'models',
        nargs='*',
        choices=['tiny', 'base', 'small', 'medium', 'large'],
        help='Specific models to download'
    )
    
    parser.add_argument(
        '--all',
        action='store_true',
        help='Download all available models'
    )
    
    parser.add_argument(
        '--recommended',
        action='store_true',
        help='Download recommended models (tiny, base, small)'
    )
    
    parser.add_argument(
        '--list',
        action='store_true',
        help='List available models and exit'
    )
    
    parser.add_argument(
        '--status',
        action='store_true',
        help='Check status of downloaded models'
    )
    
    parser.add_argument(
        '--copy-local',
        action='store_true',
        help='Copy cached models to local models directory'
    )
    
    args = parser.parse_args()
    
    print("📥 Whisper Model Downloader")
    print("=" * 30)
    
    # Handle list option
    if args.list:
        list_available_models()
        return
    
    # Handle status option
    if args.status:
        check_downloaded_models()
        return
    
    # Handle copy local option
    if args.copy_local:
        copy_models_to_local()
        return
    
    # Determine which models to download
    models_to_download = []
    
    if args.all:
        models_to_download = ['tiny', 'base', 'small', 'medium', 'large']
    elif args.recommended:
        models_to_download = ['tiny', 'base', 'small']
    elif args.models:
        models_to_download = args.models
    else:
        # Default to base model
        models_to_download = ['base']
    
    print(f"📋 Models to download: {', '.join(models_to_download)}")
    print()
    
    # Download models
    success_count = 0
    total_count = len(models_to_download)
    
    for model in models_to_download:
        if download_model(model):
            success_count += 1
        print()
    
    # Summary
    print("=" * 50)
    print(f"📊 Download Summary:")
    print(f"   Successful: {success_count}/{total_count}")
    
    if success_count > 0:
        print(f"✅ Models downloaded successfully!")
        print(f"💡 Tip: Use --copy-local to copy models for bundling")
        print(f"💡 Tip: Use --status to check downloaded models")
    
    if success_count < total_count:
        print(f"⚠️  Some downloads failed")
        sys.exit(1)


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n🛑 Download cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        sys.exit(1)

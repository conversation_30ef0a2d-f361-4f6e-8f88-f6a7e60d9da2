import os
import subprocess

video_dir = './video'
audio_dir = './audio'

# Ensure output directory exists
os.makedirs(audio_dir, exist_ok=True)

# List of common video file extensions
video_extensions = ['.mp4', '.mkv', '.avi', '.mov', '.flv', '.wmv', '.webm']

# Get all files in the video directory
for filename in os.listdir(video_dir):
    name, ext = os.path.splitext(filename)
    if ext.lower() in video_extensions:
        input_path = os.path.join(video_dir, filename)
        output_audio = os.path.join(audio_dir, f"{name}.mp3")
        command = ['ffmpeg', '-i', input_path, '-q:a', '0', '-map', 'a', output_audio]
        print(f"Extracting audio from {input_path} to {output_audio}")
        subprocess.run(command, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)

print("Done extracting audio from all videos.")

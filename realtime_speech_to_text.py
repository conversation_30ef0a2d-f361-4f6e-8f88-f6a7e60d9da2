#!/usr/bin/env python3
"""
Real-time Speech-to-Text CLI Application

This application automatically detects the best audio input device,
captures audio in real-time, and displays transcribed speech as you talk.

Usage:
    python realtime_speech_to_text.py [options]

Example:
    python realtime_speech_to_text.py --model base --language en
"""

import os
import sys
import argparse
import threading
import time
import queue
import numpy as np
import pyaudio
import whisper
from typing import Optional, List, Dict, Any, Tuple, NamedTuple
import warnings
from pathlib import Path
from datetime import datetime
import collections
import uuid
import json
import re
from dataclasses import dataclass, field

# Text improvement imports
try:
    from transformers import T5ForConditionalGeneration, T5Tokenizer
    HAS_TRANSFORMERS = True
except ImportError:
    HAS_TRANSFORMERS = False

# Suppress some warnings for cleaner output
warnings.filterwarnings("ignore", category=UserWarning)
warnings.filterwarnings("ignore", category=FutureWarning)


@dataclass
class AudioSegment:
    """Represents an audio segment with metadata for processing."""
    id: str = field(default_factory=lambda: str(uuid.uuid4())[:8])
    audio_data: np.ndarray = field(default_factory=lambda: np.array([]))
    timestamp: datetime = field(default_factory=datetime.now)
    duration: float = 0.0
    is_complete: bool = False
    is_word_boundary: bool = False
    energy_level: float = 0.0

    def __post_init__(self):
        """Calculate duration and energy after initialization."""
        if len(self.audio_data) > 0:
            self.duration = len(self.audio_data) / 16000  # Assuming 16kHz sample rate
            self.energy_level = np.sqrt(np.mean(self.audio_data ** 2))


@dataclass
class TranscriptionResult:
    """Represents a transcription result with metadata."""
    id: str
    text: str
    timestamp: datetime
    duration: float
    confidence: float = 0.0
    processing_time: float = 0.0

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        return {
            'id': self.id,
            'text': self.text,
            'timestamp': self.timestamp.isoformat(),
            'duration': self.duration,
            'confidence': self.confidence,
            'processing_time': self.processing_time
        }


@dataclass
class ImprovedTranscriptionResult:
    """Represents an improved transcription result with metadata."""
    id: str
    original_text: str
    improved_text: str
    timestamp: datetime
    duration: float
    improvement_processing_time: float = 0.0
    improvement_confidence: float = 0.0

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        return {
            'id': self.id,
            'original_text': self.original_text,
            'improved_text': self.improved_text,
            'timestamp': self.timestamp.isoformat(),
            'duration': self.duration,
            'improvement_processing_time': self.improvement_processing_time,
            'improvement_confidence': self.improvement_confidence
        }


@dataclass
class SentenceFragment:
    """Represents a fragment of text that contributes to a sentence."""
    id: str
    text: str
    timestamp: datetime
    duration: float
    is_sentence_end: bool = False
    confidence: float = 0.0

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        return {
            'id': self.id,
            'text': self.text,
            'timestamp': self.timestamp.isoformat(),
            'duration': self.duration,
            'is_sentence_end': self.is_sentence_end,
            'confidence': self.confidence
        }


@dataclass
class CompleteSentence:
    """Represents a complete sentence assembled from multiple fragments."""
    id: str = field(default_factory=lambda: str(uuid.uuid4())[:8])
    fragments: List[SentenceFragment] = field(default_factory=list)
    complete_text: str = ""
    start_timestamp: Optional[datetime] = None
    end_timestamp: Optional[datetime] = None
    total_duration: float = 0.0
    fragment_count: int = 0

    def __post_init__(self):
        """Calculate derived fields after initialization."""
        if self.fragments:
            self.complete_text = " ".join(fragment.text.strip() for fragment in self.fragments if fragment.text.strip())
            self.start_timestamp = min(fragment.timestamp for fragment in self.fragments)
            self.end_timestamp = max(fragment.timestamp for fragment in self.fragments)
            self.total_duration = sum(fragment.duration for fragment in self.fragments)
            self.fragment_count = len(self.fragments)

    def add_fragment(self, fragment: SentenceFragment):
        """Add a fragment to this sentence."""
        self.fragments.append(fragment)
        self.__post_init__()  # Recalculate derived fields

    def is_complete(self) -> bool:
        """Check if this sentence is complete (has a sentence-ending fragment)."""
        return any(fragment.is_sentence_end for fragment in self.fragments)

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        return {
            'id': self.id,
            'complete_text': self.complete_text,
            'start_timestamp': self.start_timestamp.isoformat() if self.start_timestamp else None,
            'end_timestamp': self.end_timestamp.isoformat() if self.end_timestamp else None,
            'total_duration': self.total_duration,
            'fragment_count': self.fragment_count,
            'fragments': [fragment.to_dict() for fragment in self.fragments]
        }


def find_local_model(model_name: str) -> Optional[str]:
    """
    Find a local model file in common locations.

    Args:
        model_name: Name of the model to find

    Returns:
        Path to the model file if found, None otherwise
    """
    # Possible model filenames
    model_filenames = [
        f"{model_name}.pt",
        f"{model_name}-v3.pt" if model_name == "large" else f"{model_name}.pt"
    ]

    # Search locations (in order of preference)
    search_paths = [
        # Local models directory (for bundled distributions)
        Path("./models"),
        Path("./whisper_models"),

        # Relative to executable (for PyInstaller)
        Path(sys.executable).parent / "models",
        Path(sys.executable).parent / "whisper_models",

        # Standard cache locations
        Path.home() / ".cache" / "whisper",
        Path(os.environ.get("APPDATA", "")) / "whisper" if os.name == "nt" else Path.home() / ".cache" / "whisper",
    ]

    for search_path in search_paths:
        if search_path.exists():
            for filename in model_filenames:
                model_path = search_path / filename
                if model_path.exists():
                    return str(model_path)

    return None


def load_whisper_model(model_name: str):
    """
    Load a Whisper model, preferring local files over downloads.

    Args:
        model_name: Name of the model to load

    Returns:
        Loaded Whisper model
    """
    # Try to find local model first
    local_model_path = find_local_model(model_name)

    if local_model_path:
        try:
            return whisper.load_model(local_model_path)
        except Exception as e:
            print(f"❌ Failed to load local model: {e}")

    # Fall back to standard download
    return whisper.load_model(model_name)


class SentenceBuffer:
    """Thread-safe buffer for accumulating text fragments into complete sentences."""

    def __init__(self, max_sentence_age_seconds: float = 10.0, max_buffer_size: int = 50):
        """
        Initialize the sentence buffer.

        Args:
            max_sentence_age_seconds: Maximum age of incomplete sentences before forcing completion
            max_buffer_size: Maximum number of incomplete sentences to keep in buffer
        """
        self.lock = threading.Lock()
        self.incomplete_sentences: Dict[str, CompleteSentence] = {}
        self.max_sentence_age = max_sentence_age_seconds
        self.max_buffer_size = max_buffer_size
        self.sentence_end_patterns = [
            r'[.!?]+\s*$',  # Ends with punctuation
            r'[.!?]+\s+[A-Z]',  # Punctuation followed by capital letter
            r'\.\s*$',  # Ends with period
            r'!\s*$',   # Ends with exclamation
            r'\?\s*$',  # Ends with question mark
        ]

    def add_fragment(self, transcription_result: 'TranscriptionResult') -> List[CompleteSentence]:
        """
        Add a transcription fragment and return any complete sentences.

        Args:
            transcription_result: The transcription result to add

        Returns:
            List of complete sentences ready for processing
        """
        with self.lock:
            # Create sentence fragment
            is_sentence_end = self._detect_sentence_boundary(transcription_result.text)
            fragment = SentenceFragment(
                id=transcription_result.id,
                text=transcription_result.text,
                timestamp=transcription_result.timestamp,
                duration=transcription_result.duration,
                is_sentence_end=is_sentence_end,
                confidence=transcription_result.confidence
            )

            # Find or create sentence to add this fragment to
            sentence = self._find_or_create_sentence(fragment)
            sentence.add_fragment(fragment)

            # Check for complete sentences
            complete_sentences = self._extract_complete_sentences()

            # Clean up old incomplete sentences
            self._cleanup_old_sentences()

            return complete_sentences

    def _detect_sentence_boundary(self, text: str) -> bool:
        """Detect if text contains a sentence boundary."""
        if not text or not text.strip():
            return False

        text = text.strip()

        # Check for sentence-ending patterns
        for pattern in self.sentence_end_patterns:
            if re.search(pattern, text):
                return True

        return False

    def _find_or_create_sentence(self, fragment: SentenceFragment) -> CompleteSentence:
        """Find existing incomplete sentence or create new one."""
        # For now, create a new sentence for each fragment
        # In a more sophisticated implementation, we could try to group
        # fragments that are close in time
        sentence_id = f"sent_{fragment.id}"

        if sentence_id not in self.incomplete_sentences:
            self.incomplete_sentences[sentence_id] = CompleteSentence(id=sentence_id)

        return self.incomplete_sentences[sentence_id]

    def _extract_complete_sentences(self) -> List[CompleteSentence]:
        """Extract and remove complete sentences from buffer."""
        complete_sentences = []
        to_remove = []

        for sentence_id, sentence in self.incomplete_sentences.items():
            if sentence.is_complete():
                complete_sentences.append(sentence)
                to_remove.append(sentence_id)

        # Remove complete sentences from buffer
        for sentence_id in to_remove:
            del self.incomplete_sentences[sentence_id]

        return complete_sentences

    def _cleanup_old_sentences(self):
        """Remove sentences that are too old or when buffer is too full."""
        current_time = datetime.now()
        to_remove = []

        # Remove old sentences
        for sentence_id, sentence in self.incomplete_sentences.items():
            if sentence.start_timestamp:
                age = (current_time - sentence.start_timestamp).total_seconds()
                if age > self.max_sentence_age:
                    to_remove.append(sentence_id)

        # Remove oldest sentences if buffer is too full
        if len(self.incomplete_sentences) > self.max_buffer_size:
            # Sort by start timestamp and remove oldest
            sorted_sentences = sorted(
                self.incomplete_sentences.items(),
                key=lambda x: x[1].start_timestamp or datetime.min
            )
            excess_count = len(self.incomplete_sentences) - self.max_buffer_size
            for i in range(excess_count):
                to_remove.append(sorted_sentences[i][0])

        # Remove identified sentences
        for sentence_id in to_remove:
            if sentence_id in self.incomplete_sentences:
                del self.incomplete_sentences[sentence_id]

    def force_complete_all(self) -> List[CompleteSentence]:
        """Force completion of all incomplete sentences and return them."""
        with self.lock:
            complete_sentences = list(self.incomplete_sentences.values())
            self.incomplete_sentences.clear()
            return complete_sentences

    def get_buffer_status(self) -> Dict[str, Any]:
        """Get current buffer status for monitoring."""
        with self.lock:
            return {
                'incomplete_count': len(self.incomplete_sentences),
                'oldest_sentence_age': self._get_oldest_sentence_age(),
                'buffer_utilization': len(self.incomplete_sentences) / self.max_buffer_size
            }

    def _get_oldest_sentence_age(self) -> float:
        """Get age of oldest incomplete sentence in seconds."""
        if not self.incomplete_sentences:
            return 0.0

        current_time = datetime.now()
        oldest_time = min(
            sentence.start_timestamp or current_time
            for sentence in self.incomplete_sentences.values()
        )
        return (current_time - oldest_time).total_seconds()


class TextImprovementProcessor:
    """Handles text improvement using T5 models for grammar correction and enhancement."""

    def __init__(self, model_variant: str = 'base', enable_improvement: bool = True):
        """
        Initialize the text improvement processor.

        Args:
            model_variant: T5 model variant ('mini', 'small', 'base')
            enable_improvement: Whether to enable text improvement
        """
        self.enable_improvement = enable_improvement and HAS_TRANSFORMERS
        self.model_variant = model_variant
        self.model = None
        self.tokenizer = None
        self.model_loaded = False

        # Sentence-level processing
        self.sentence_buffer = SentenceBuffer()

        if self.enable_improvement:
            self._load_model()

    def _load_model(self):
        """Load the T5 model for text improvement."""
        try:
            # Map model variants to actual model names
            model_mapping = {
                'mini': 't5-small',  # Use t5-small as mini equivalent
                'small': 't5-small',
                'base': 't5-base'
            }

            model_name = model_mapping.get(self.model_variant, 't5-base')

            self.tokenizer = T5Tokenizer.from_pretrained(model_name)
            self.model = T5ForConditionalGeneration.from_pretrained(model_name)

            self.model_loaded = True

        except Exception as e:
            print(f"❌ Failed to load T5 model: {e}")
            self.enable_improvement = False
            self.model_loaded = False

    def improve_text(self, text: str) -> str:
        """
        Improve text using T5 model for grammar correction and enhancement.

        Args:
            text: Original text to improve

        Returns:
            Improved text or original text if improvement fails
        """
        if not self.enable_improvement or not self.model_loaded or not text.strip():
            return text

        try:
            # Use different task prefixes for better results
            task_prefixes = [
                f"fix grammar: {text}",
                f"correct: {text}",
                f"improve: {text}"
            ]

            best_result = text
            best_score = 0

            for prefix in task_prefixes:
                try:
                    # Tokenize input
                    input_ids = self.tokenizer.encode(prefix, return_tensors='pt', max_length=256, truncation=True)

                    # Generate improved text with more conservative settings
                    outputs = self.model.generate(
                        input_ids,
                        max_length=256,
                        num_beams=3,
                        early_stopping=True,
                        do_sample=False,
                        no_repeat_ngram_size=2,
                        length_penalty=1.0
                    )

                    # Decode the improved text
                    improved_text = self.tokenizer.decode(outputs[0], skip_special_tokens=True)

                    # Basic validation and scoring
                    if improved_text and len(improved_text.strip()) > 0:
                        # Simple scoring: prefer results that are similar in length and don't repeat
                        score = self._score_improvement(text, improved_text)
                        if score > best_score:
                            best_result = improved_text.strip()
                            best_score = score

                except Exception:
                    continue

            return best_result

        except Exception as e:
            print(f"❌ Error improving text: {e}")
            return text

    def improve_sentence(self, sentence: CompleteSentence) -> str:
        """
        Improve a complete sentence using T5 model with sentence-level optimization.

        Args:
            sentence: Complete sentence to improve

        Returns:
            Improved sentence text preserving all original words
        """
        if not self.enable_improvement or not self.model_loaded:
            return sentence.complete_text

        # Use the existing improve_text method but with sentence-optimized approach
        original_text = sentence.complete_text.strip()
        if not original_text:
            return original_text

        try:
            # For sentences, use more specific task prefixes
            sentence_prefixes = [
                f"improve grammar and readability: {original_text}",
                f"correct grammar while preserving meaning: {original_text}",
                f"enhance sentence structure: {original_text}"
            ]

            best_result = original_text
            best_score = 0

            for prefix in sentence_prefixes:
                try:
                    # Tokenize input with sentence-appropriate length
                    input_ids = self.tokenizer.encode(prefix, return_tensors='pt', max_length=512, truncation=True)

                    # Generate improved text with sentence-optimized settings
                    outputs = self.model.generate(
                        input_ids,
                        max_length=512,
                        num_beams=4,  # More beams for better sentence quality
                        early_stopping=True,
                        do_sample=False,
                        no_repeat_ngram_size=3,  # Prevent repetition in longer sentences
                        length_penalty=1.2,  # Slight preference for appropriate length
                        repetition_penalty=1.1  # Discourage repetition
                    )

                    # Decode the improved text
                    improved_text = self.tokenizer.decode(outputs[0], skip_special_tokens=True)

                    # Enhanced validation for sentence-level improvement
                    if improved_text and len(improved_text.strip()) > 0:
                        score = self._score_sentence_improvement(original_text, improved_text)
                        if score > best_score:
                            best_result = improved_text.strip()
                            best_score = score

                except Exception:
                    continue

            return best_result

        except Exception as e:
            print(f"❌ Error improving sentence: {e}")
            return original_text

    def _score_sentence_improvement(self, original: str, improved: str) -> float:
        """Score the quality of sentence-level text improvement."""
        if not improved or improved == original:
            return 0.0

        # More sophisticated scoring for sentences
        original_words = set(original.lower().split())
        improved_words = set(improved.lower().split())

        # Penalize if too many original words are lost (preserve original content)
        word_preservation = len(original_words.intersection(improved_words)) / max(len(original_words), 1)
        if word_preservation < 0.7:  # At least 70% of original words should be preserved
            return 0.0

        # Penalize if too different in length
        length_ratio = len(improved) / max(len(original), 1)
        if length_ratio > 2.0 or length_ratio < 0.5:
            return 0.0

        # Penalize repetitive text
        words = improved.lower().split()
        if len(words) > 3:
            unique_words = len(set(words))
            repetition_penalty = unique_words / len(words)
            if repetition_penalty < 0.6:  # Too repetitive
                return 0.0

        # Reward good word preservation and reasonable length
        score = word_preservation * 0.7 + min(length_ratio, 1.0) * 0.3
        return score

    def _score_improvement(self, original: str, improved: str) -> float:
        """Score the quality of text improvement."""
        if not improved or improved == original:
            return 0.0

        # Penalize if too different in length
        length_ratio = len(improved) / max(len(original), 1)
        if length_ratio > 3.0 or length_ratio < 0.3:
            return 0.0

        # Penalize repetitive text
        words = improved.lower().split()
        if len(words) > 5:
            unique_words = len(set(words))
            repetition_penalty = unique_words / len(words)
            if repetition_penalty < 0.5:  # Too repetitive
                return 0.0

        # Basic scoring
        return 1.0

    def process_transcription_for_sentences(self, transcription_result: 'TranscriptionResult') -> List[CompleteSentence]:
        """
        Process a transcription result and return any complete sentences.

        Args:
            transcription_result: The transcription result to process

        Returns:
            List of complete sentences ready for improvement
        """
        return self.sentence_buffer.add_fragment(transcription_result)

    def force_complete_all_sentences(self) -> List[CompleteSentence]:
        """Force completion of all incomplete sentences."""
        return self.sentence_buffer.force_complete_all()

    def get_sentence_buffer_status(self) -> Dict[str, Any]:
        """Get current sentence buffer status."""
        return self.sentence_buffer.get_buffer_status()

    def is_available(self) -> bool:
        """Check if text improvement is available."""
        return self.enable_improvement and self.model_loaded


class VoiceActivityDetector:
    """Enhanced Voice Activity Detection with word boundary detection."""

    def __init__(self, sample_rate: int = 16000, frame_duration_ms: int = 30):
        """
        Initialize Voice Activity Detector.

        Args:
            sample_rate: Audio sample rate in Hz
            frame_duration_ms: Frame duration in milliseconds
        """
        self.sample_rate = sample_rate
        self.frame_duration_ms = frame_duration_ms
        self.frame_size = int(sample_rate * frame_duration_ms / 1000)

        # VAD parameters (optimized for practical use)
        self.energy_threshold = 0.002  # Base threshold
        self.silence_duration_ms = 800  # Longer silence for complete speech segments
        self.speech_pad_ms = 300  # Adequate padding for better capture
        self.min_speech_frames = 15  # Higher minimum for better quality
        self.speech_multiplier = 2.5  # Speech energy multiplier

        # Word boundary detection parameters (more conservative)
        self.word_boundary_silence_ms = 500  # Longer silence for word boundaries
        self.max_segment_duration_ms = 1000  # Maximum segment duration (1 second)
        self.word_boundary_frames = int(self.word_boundary_silence_ms / frame_duration_ms)
        self.max_segment_frames = int(self.max_segment_duration_ms / frame_duration_ms)

        # State tracking
        self.silence_frames = int(self.silence_duration_ms / frame_duration_ms)
        self.pad_frames = int(self.speech_pad_ms / frame_duration_ms)
        self.consecutive_silence = 0
        self.is_speaking = False
        self.speech_start_time = None

        # Enhanced buffer management
        self.pre_speech_buffer = collections.deque(maxlen=self.pad_frames)
        self.current_segment = AudioSegment()
        self.segment_frames = []
        self.segment_start_time = None

        # Energy history for adaptive threshold
        self.energy_history = collections.deque(maxlen=100)
        self.adaptive_threshold = True

        # Debug and statistics
        self.frame_count = 0
        self.debug_interval = 100
        self.debug_enabled = False
        self.segments_created = 0

    def calculate_energy(self, audio_frame: np.ndarray) -> float:
        """Calculate RMS energy of audio frame."""
        if len(audio_frame) == 0:
            return 0.0
        return np.sqrt(np.mean(audio_frame ** 2))

    def update_adaptive_threshold(self, energy: float):
        """Update adaptive energy threshold based on background noise."""
        self.energy_history.append(energy)
        if len(self.energy_history) >= 50:  # Need enough samples
            # Set threshold based on background noise level
            mean_energy = np.mean(self.energy_history)
            std_energy = np.std(self.energy_history)
            # Threshold should be background + some margin, but not too high
            adaptive_threshold = mean_energy + (std_energy * self.speech_multiplier)
            self.energy_threshold = max(0.001, min(0.01, adaptive_threshold))

    def process_frame(self, audio_frame: np.ndarray) -> Tuple[bool, Optional[AudioSegment]]:
        """
        Process audio frame with enhanced timing and word boundary detection.

        Args:
            audio_frame: Audio frame to process

        Returns:
            Tuple of (is_voice_detected, audio_segment_if_ready)
        """
        # Process the frame in smaller chunks if it's larger than expected
        segments_ready = []
        is_voice_detected = False

        # Split large frames into smaller VAD-sized chunks
        start_idx = 0
        while start_idx < len(audio_frame):
            end_idx = min(start_idx + self.frame_size, len(audio_frame))
            chunk = audio_frame[start_idx:end_idx]

            # Pad chunk to expected size if needed
            if len(chunk) < self.frame_size:
                chunk = np.pad(chunk, (0, self.frame_size - len(chunk)))

            voice_detected, segment = self._process_single_frame(chunk)
            if voice_detected:
                is_voice_detected = True
            if segment:
                segments_ready.append(segment)
            start_idx = end_idx

        # Return the most recent segment if any are ready
        return is_voice_detected, segments_ready[-1] if segments_ready else None

    def _process_single_frame(self, audio_frame: np.ndarray) -> Tuple[bool, Optional[AudioSegment]]:
        """Process a single frame with enhanced timing and word boundary detection."""
        current_time = datetime.now()

        # Calculate energy
        energy = self.calculate_energy(audio_frame)

        # Update adaptive threshold
        if self.adaptive_threshold:
            self.update_adaptive_threshold(energy)

        # Debug output every N frames (if enabled)
        self.frame_count += 1

        # Voice activity detection
        is_voice = energy > self.energy_threshold

        # Always add to pre-speech buffer for padding
        self.pre_speech_buffer.append(audio_frame)

        if is_voice:
            if not self.is_speaking:
                # Start of new speech segment
                self.is_speaking = True
                self.speech_start_time = current_time
                self.segment_start_time = current_time
                self.segment_frames = list(self.pre_speech_buffer)  # Include padding
            else:
                # Continue recording speech
                self.segment_frames.append(audio_frame)

            self.consecutive_silence = 0

            # Check if we should create a segment based on time (1 second rule)
            if self.segment_start_time:
                elapsed_ms = (current_time - self.segment_start_time).total_seconds() * 1000
                if elapsed_ms >= self.max_segment_duration_ms and len(self.segment_frames) >= self.min_speech_frames:
                    return self._create_segment(is_complete=False, is_word_boundary=False)
        else:
            if self.is_speaking:
                # Silent frame during speech
                self.segment_frames.append(audio_frame)
                self.consecutive_silence += 1

                # Check for word boundary (shorter silence)
                if self.consecutive_silence >= self.word_boundary_frames:
                    if len(self.segment_frames) >= self.min_speech_frames:
                        return self._create_segment(is_complete=False, is_word_boundary=True)

                # Check for end of speech (longer silence)
                if self.consecutive_silence >= self.silence_frames:
                    if len(self.segment_frames) >= self.min_speech_frames:
                        return self._create_segment(is_complete=True, is_word_boundary=False)
                    else:
                        # Reset without creating segment
                        self._reset_segment_state()

        return is_voice, None

    def _create_segment(self, is_complete: bool, is_word_boundary: bool) -> Tuple[bool, AudioSegment]:
        """Create an AudioSegment from current frames."""
        if not self.segment_frames:
            return False, None

        # Create audio segment
        audio_data = np.concatenate(self.segment_frames)
        segment = AudioSegment(
            audio_data=audio_data,
            timestamp=self.segment_start_time or datetime.now(),
            is_complete=is_complete,
            is_word_boundary=is_word_boundary
        )

        self.segments_created += 1
        duration = segment.duration

        if is_complete:
            self._reset_segment_state()
        elif is_word_boundary:
            self._start_new_segment()
        else:
            self._start_new_segment()

        return False, segment

    def _reset_segment_state(self):
        """Reset all segment state for new speech detection."""
        self.is_speaking = False
        self.consecutive_silence = 0
        self.segment_frames = []
        self.segment_start_time = None
        self.speech_start_time = None

    def _start_new_segment(self):
        """Start a new segment while continuing speech detection."""
        self.consecutive_silence = 0
        self.segment_frames = []
        self.segment_start_time = datetime.now()


class AudioDeviceManager:
    """Manages audio input devices and selects the best one."""
    
    def __init__(self):
        self.audio = pyaudio.PyAudio()
    
    def get_audio_devices(self) -> List[Dict[str, Any]]:
        """Get all available audio input devices."""
        devices = []
        device_count = self.audio.get_device_count()
        
        for i in range(device_count):
            try:
                device_info = self.audio.get_device_info_by_index(i)
                if device_info['maxInputChannels'] > 0:  # Input device
                    devices.append({
                        'index': i,
                        'name': device_info['name'],
                        'channels': device_info['maxInputChannels'],
                        'sample_rate': int(device_info['defaultSampleRate']),
                        'host_api': self.audio.get_host_api_info_by_index(device_info['hostApi'])['name']
                    })
            except Exception:
                continue
        
        return devices
    
    def select_best_device(self) -> Optional[Dict[str, Any]]:
        """Select the best available audio input device."""
        devices = self.get_audio_devices()
        
        if not devices:
            return None
        
        # Prioritize devices with higher sample rates and more channels
        # Also prefer certain host APIs (DirectSound, WASAPI on Windows)
        def device_score(device):
            score = 0
            score += device['sample_rate'] / 1000  # Higher sample rate is better
            score += device['channels'] * 10  # More channels is better
            
            # Prefer certain host APIs
            preferred_apis = ['WASAPI', 'DirectSound', 'Core Audio', 'ALSA']
            if device['host_api'] in preferred_apis:
                score += 100
            
            # Prefer devices with "microphone" or "mic" in the name
            name_lower = device['name'].lower()
            if 'microphone' in name_lower or 'mic' in name_lower:
                score += 50
            
            return score
        
        best_device = max(devices, key=device_score)
        return best_device
    
    def list_devices(self):
        """Print all available audio input devices."""
        devices = self.get_audio_devices()

        print("\nAvailable Audio Input Devices:")
        print("-" * 60)

        for device in devices:
            print(f"  [{device['index']}] {device['name']}")
            print(f"      Channels: {device['channels']}, Sample Rate: {device['sample_rate']} Hz")
            print(f"      Host API: {device['host_api']}")
            print()
    
    def cleanup(self):
        """Clean up PyAudio resources."""
        self.audio.terminate()


class RealTimeSpeechToText:
    """Enhanced real-time speech-to-text with true parallel processing."""

    def __init__(self, model_name: str = 'base', language: Optional[str] = None,
                 output_file: Optional[str] = None, debug_vad: bool = False,
                 t5_model: str = 'base', enable_improvement: bool = False):
        """
        Initialize the enhanced real-time speech-to-text converter.

        Args:
            model_name: Whisper model to use
            language: Language code (e.g., 'en', 'es', 'fr')
            output_file: Path to output text file (optional)
            debug_vad: Enable VAD debug output
            t5_model: T5 model variant for text improvement ('mini', 'small', 'base')
            enable_improvement: Enable text improvement using T5 model
        """
        self.model = load_whisper_model(model_name)
        self.language = language

        # Text improvement processor
        self.text_processor = TextImprovementProcessor(t5_model, enable_improvement)
        self.enable_improvement = self.text_processor.is_available()

        # Audio settings
        self.sample_rate = 16000  # Whisper expects 16kHz
        self.frame_duration_ms = 30  # VAD frame duration
        self.min_speech_duration = 0.5  # Minimum duration for transcription (increased for quality)

        # Voice Activity Detection
        self.vad = VoiceActivityDetector(self.sample_rate, self.frame_duration_ms)
        self.vad.debug_enabled = debug_vad

        # Enhanced queue system with size limits
        self.raw_audio_queue = queue.Queue(maxsize=100)  # Raw audio frames
        self.audio_segment_queue = queue.Queue(maxsize=50)  # AudioSegment objects
        self.transcription_queue = queue.Queue(maxsize=100)  # TranscriptionResult objects
        self.text_improvement_queue = queue.Queue(maxsize=100)  # For text improvement processing (legacy)
        self.sentence_queue = queue.Queue(maxsize=50)  # Complete sentences for improvement
        self.improved_transcription_queue = queue.Queue(maxsize=100)  # ImprovedTranscriptionResult objects

        # Thread management
        self.is_running = False
        self.threads = {}
        self.device_manager = AudioDeviceManager()

        # Output management
        self.output_file = output_file
        self.temp_transcriptions = []  # Temporary buffer for transcriptions
        self.temp_improved_transcriptions = []  # Temporary buffer for improved transcriptions
        self.output_lock = threading.Lock()
        self.final_output_file = "final_transcribed.txt"
        self.improved_output_file = "improved_transcribed.txt"

        # Statistics and monitoring
        self.segments_processed = 0
        self.transcription_errors = 0
        self.improvements_processed = 0
        self.improvement_errors = 0
        self.start_time = None
        self.session_id = str(uuid.uuid4())[:8]
        
    def audio_callback(self, in_data, frame_count, time_info, status):
        """Enhanced audio callback with queue overflow protection."""
        if status:
            print(f"❌ Audio status: {status}")

        # Convert bytes to numpy array
        audio_data = np.frombuffer(in_data, dtype=np.float32)

        # Non-blocking queue put with overflow protection
        try:
            self.raw_audio_queue.put_nowait(audio_data)
        except queue.Full:
            # Drop oldest frame if queue is full
            try:
                self.raw_audio_queue.get_nowait()
                self.raw_audio_queue.put_nowait(audio_data)
            except queue.Empty:
                pass

        return (in_data, pyaudio.paContinue)
    
    def voice_detection_thread(self):
        """Enhanced voice detection thread with AudioSegment processing."""
        thread_id = threading.current_thread().ident

        while self.is_running:
            try:
                # Get raw audio data with timeout
                audio_chunk = self.raw_audio_queue.get(timeout=0.1)

                # Process with enhanced VAD
                is_voice, audio_segment = self.vad.process_frame(audio_chunk)

                # If we have a ready audio segment, queue it for transcription
                if audio_segment is not None:
                    # Check minimum duration
                    if audio_segment.duration >= self.min_speech_duration:
                        try:
                            self.audio_segment_queue.put_nowait(audio_segment)
                        except queue.Full:
                            print("❌ Audio segment queue full, dropping oldest segment")
                            try:
                                self.audio_segment_queue.get_nowait()
                                self.audio_segment_queue.put_nowait(audio_segment)
                            except queue.Empty:
                                pass

            except queue.Empty:
                continue
            except Exception as e:
                print(f"❌ Error in voice detection thread (ID: {thread_id}): {e}")
                if not self.is_running:
                    break

    def transcription_thread(self):
        """Enhanced transcription thread with proper error handling."""
        thread_id = threading.current_thread().ident

        while self.is_running:
            try:
                # Get audio segment to transcribe
                audio_segment = self.audio_segment_queue.get(timeout=0.1)

                # Transcribe the audio segment
                start_time = time.time()
                text = self.transcribe_audio_segment(audio_segment)
                processing_time = time.time() - start_time

                if text:
                    # Create transcription result
                    result = TranscriptionResult(
                        id=audio_segment.id,
                        text=text,
                        timestamp=audio_segment.timestamp,
                        duration=audio_segment.duration,
                        processing_time=processing_time
                    )

                    # Queue transcription result for output
                    try:
                        self.transcription_queue.put_nowait(result)
                        self.segments_processed += 1

                        # Process for sentence-level improvement if enabled
                        if self.enable_improvement:
                            # Process transcription for sentence completion
                            complete_sentences = self.text_processor.process_transcription_for_sentences(result)

                            # Queue any complete sentences for improvement
                            for sentence in complete_sentences:
                                try:
                                    self.sentence_queue.put_nowait(sentence)
                                except queue.Full:
                                    print("❌ Sentence queue full, dropping oldest sentence")
                                    try:
                                        self.sentence_queue.get_nowait()
                                        self.sentence_queue.put_nowait(sentence)
                                    except queue.Empty:
                                        pass

                    except queue.Full:
                        print("❌ Transcription queue full, dropping oldest result")
                        try:
                            self.transcription_queue.get_nowait()
                            self.transcription_queue.put_nowait(result)
                        except queue.Empty:
                            pass

            except queue.Empty:
                continue
            except Exception as e:
                self.transcription_errors += 1
                print(f"❌ Error in transcription thread (ID: {thread_id}): {e}")
                if not self.is_running:
                    break

    def transcribe_audio_segment(self, audio_segment: AudioSegment) -> str:
        """Transcribe an AudioSegment using Whisper."""
        try:
            # Ensure minimum length for Whisper
            min_samples = int(0.1 * self.sample_rate)  # 0.1 second minimum
            if len(audio_segment.audio_data) < min_samples:
                return ""

            # Transcribe the audio segment
            result = self.model.transcribe(
                audio_segment.audio_data,
                language=self.language,
                verbose=False,
                fp16=False  # Use fp32 for better compatibility
            )

            text = result['text'].strip()
            return text if text else ""

        except Exception as e:
            print(f"❌ Error transcribing audio segment {audio_segment.id}: {e}")
            return ""

    def text_improvement_thread(self):
        """Sentence-by-sentence text improvement thread using T5 model."""
        thread_id = threading.current_thread().ident

        while self.is_running:
            try:
                # Get complete sentence to improve
                sentence = self.sentence_queue.get(timeout=0.1)

                # Improve the sentence using sentence-level optimization
                start_time = time.time()
                improved_text = self.text_processor.improve_sentence(sentence)
                processing_time = time.time() - start_time

                # Create improved transcription result from sentence
                improved_result = ImprovedTranscriptionResult(
                    id=sentence.id,
                    original_text=sentence.complete_text,
                    improved_text=improved_text,
                    timestamp=sentence.start_timestamp or datetime.now(),
                    duration=sentence.total_duration,
                    improvement_processing_time=processing_time
                )

                # Queue improved result for output
                try:
                    self.improved_transcription_queue.put_nowait(improved_result)
                    self.improvements_processed += 1
                except queue.Full:
                    print("❌ Improved transcription queue full, dropping oldest result")
                    try:
                        self.improved_transcription_queue.get_nowait()
                        self.improved_transcription_queue.put_nowait(improved_result)
                    except queue.Empty:
                        pass

            except queue.Empty:
                # No complete sentences available, continue waiting
                continue
            except Exception as e:
                self.improvement_errors += 1
                print(f"❌ Error in sentence improvement thread (ID: {thread_id}): {e}")
                if not self.is_running:
                    break

    def output_thread(self):
        """Enhanced output thread with temporary buffer management."""
        thread_id = threading.current_thread().ident

        while self.is_running:
            try:
                # Get transcription result
                result = self.transcription_queue.get(timeout=0.1)

                # Format timestamp for display
                display_time = result.timestamp.strftime("%H:%M:%S")

                # Display to console immediately
                print(f"[{display_time}] {result.text}")

                # Add to temporary buffer
                with self.output_lock:
                    self.temp_transcriptions.append(result)

                # Write to temporary file if specified
                if self.output_file:
                    self.write_to_temp_file(result)

            except queue.Empty:
                continue
            except Exception as e:
                print(f"❌ Error in output thread (ID: {thread_id}): {e}")
                if not self.is_running:
                    break

    def write_to_temp_file(self, result: TranscriptionResult):
        """Write transcription result to temporary file."""
        try:
            timestamp_str = result.timestamp.strftime("%H:%M:%S")
            with open(self.output_file, 'a', encoding='utf-8') as f:
                f.write(f"[{timestamp_str}] {result.text}\n")
                f.flush()  # Ensure immediate write
        except Exception as e:
            print(f"❌ Error writing to temp file: {e}")

    def improved_output_thread(self):
        """Output thread for sentence-level improved transcriptions."""
        thread_id = threading.current_thread().ident

        while self.is_running:
            try:
                # Get improved transcription result
                result = self.improved_transcription_queue.get(timeout=0.1)

                # Format timestamp for display
                display_time = result.timestamp.strftime("%H:%M:%S")

                # Display to console with sentence-level comparison
                print(f"[{display_time}] 📝 Sentence Original: {result.original_text}")
                print(f"[{display_time}] ✨ Sentence Improved: {result.improved_text}")
                print()  # Add spacing for readability

                # Add to temporary buffer
                with self.output_lock:
                    self.temp_improved_transcriptions.append(result)

                # Write to improved file if specified
                self.write_to_improved_file(result)

            except queue.Empty:
                continue
            except Exception as e:
                print(f"❌ Error in sentence improvement output thread (ID: {thread_id}): {e}")
                if not self.is_running:
                    break

    def write_to_improved_file(self, result: ImprovedTranscriptionResult):
        """Write sentence-level improved transcription result to file."""
        try:
            timestamp_str = result.timestamp.strftime("%H:%M:%S")
            with open(self.improved_output_file, 'a', encoding='utf-8') as f:
                f.write(f"[{timestamp_str}] Sentence Original: {result.original_text}\n")
                f.write(f"[{timestamp_str}] Sentence Improved: {result.improved_text}\n")
                f.write(f"[{timestamp_str}] Processing Time: {result.improvement_processing_time:.3f}s\n\n")
                f.flush()  # Ensure immediate write
        except Exception as e:
            print(f"❌ Error writing to improved file: {e}")

    def consolidate_final_output(self):
        """Consolidate all transcriptions into final output file."""
        try:
            with self.output_lock:
                if not self.temp_transcriptions:
                    return

                # Sort transcriptions by timestamp
                sorted_transcriptions = sorted(self.temp_transcriptions, key=lambda x: x.timestamp)

                # Write to final file
                with open(self.final_output_file, 'w', encoding='utf-8') as f:
                    f.write(f"# Speech-to-Text Session {self.session_id}\n")
                    f.write(f"# Generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                    f.write(f"# Total segments: {len(sorted_transcriptions)}\n")
                    f.write(f"# Session duration: {time.time() - self.start_time:.1f} seconds\n\n")

                    for result in sorted_transcriptions:
                        timestamp_str = result.timestamp.strftime("%H:%M:%S")
                        f.write(f"[{timestamp_str}] {result.text}\n")

                # Also consolidate improved transcriptions if available
                if self.enable_improvement and self.temp_improved_transcriptions:
                    sorted_improved = sorted(self.temp_improved_transcriptions, key=lambda x: x.timestamp)

                    with open(self.improved_output_file, 'w', encoding='utf-8') as f:
                        f.write(f"# Sentence-Level Improved Speech-to-Text Session {self.session_id}\n")
                        f.write(f"# Generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                        f.write(f"# Total improved sentences: {len(sorted_improved)}\n")
                        f.write(f"# Session duration: {time.time() - self.start_time:.1f} seconds\n")
                        f.write(f"# Processing method: Sentence-by-sentence T5 improvement\n\n")

                        for result in sorted_improved:
                            timestamp_str = result.timestamp.strftime("%H:%M:%S")
                            f.write(f"[{timestamp_str}] Sentence Original: {result.original_text}\n")
                            f.write(f"[{timestamp_str}] Sentence Improved: {result.improved_text}\n")
                            f.write(f"[{timestamp_str}] Processing Time: {result.improvement_processing_time:.3f}s\n\n")

        except Exception as e:
            print(f"❌ Error consolidating final output: {e}")

    def print_statistics(self):
        """Print enhanced processing statistics."""
        pass
    
    def start_listening(self):
        """Start enhanced real-time audio capture with true parallel processing."""
        # Select the best audio device
        device = self.device_manager.select_best_device()

        if not device:
            print("❌ No audio input devices found!")
            return False



        try:
            # Start audio stream
            stream = self.device_manager.audio.open(
                format=pyaudio.paFloat32,
                channels=1,  # Mono
                rate=self.sample_rate,
                input=True,
                input_device_index=device['index'],
                frames_per_buffer=1024,
                stream_callback=self.audio_callback
            )

            # Start all processing threads with enhanced management
            self.is_running = True
            self.start_time = time.time()

            # Voice detection thread (Core 1: Real-time audio processing)
            self.threads['vad'] = threading.Thread(target=self.voice_detection_thread, name="VAD-Thread")
            self.threads['vad'].daemon = True
            self.threads['vad'].start()

            # Transcription thread (Core 2: Parallel transcription processing)
            self.threads['transcription'] = threading.Thread(target=self.transcription_thread, name="Transcription-Thread")
            self.threads['transcription'].daemon = True
            self.threads['transcription'].start()

            # Text improvement thread (Core 3: Text enhancement processing)
            if self.enable_improvement:
                self.threads['text_improvement'] = threading.Thread(target=self.text_improvement_thread, name="TextImprovement-Thread")
                self.threads['text_improvement'].daemon = True
                self.threads['text_improvement'].start()

            # Output thread (Core 4: Output management)
            self.threads['output'] = threading.Thread(target=self.output_thread, name="Output-Thread")
            self.threads['output'].daemon = True
            self.threads['output'].start()

            # Improved output thread (Core 5: Improved output management)
            if self.enable_improvement:
                self.threads['improved_output'] = threading.Thread(target=self.improved_output_thread, name="ImprovedOutput-Thread")
                self.threads['improved_output'].daemon = True
                self.threads['improved_output'].start()

            # Start the audio stream
            stream.start_stream()



            # Keep the main thread alive and monitor threads
            try:
                while stream.is_active() and self.is_running:
                    time.sleep(0.5)

                    # Check if all threads are still alive
                    dead_threads = [name for name, thread in self.threads.items() if not thread.is_alive()]
                    if dead_threads:
                        print(f"❌ Warning: Threads died: {dead_threads}")

            except KeyboardInterrupt:
                pass

            # Graceful shutdown
            self.is_running = False

            # Wait for threads to finish processing remaining items
            for name, thread in self.threads.items():
                thread.join(timeout=2.0)
                if thread.is_alive():
                    print(f"❌ Thread {name} did not finish gracefully")

            # Stop audio stream
            stream.stop_stream()
            stream.close()

            # Consolidate final output
            self.consolidate_final_output()

            # Print final statistics
            self.print_statistics()

            return True

        except Exception as e:
            print(f"❌ Error starting enhanced audio stream: {e}")
            return False

    def cleanup(self):
        """Enhanced cleanup with queue clearing and final consolidation."""
        self.is_running = False

        # Force completion of any incomplete sentences before cleanup
        if self.enable_improvement:
            try:
                incomplete_sentences = self.text_processor.force_complete_all_sentences()
                for sentence in incomplete_sentences:
                    try:
                        self.sentence_queue.put_nowait(sentence)
                    except queue.Full:
                        break  # Don't block during cleanup
            except Exception as e:
                print(f"❌ Error forcing sentence completion during cleanup: {e}")

        # Clear all queues
        self._clear_queue(self.raw_audio_queue, "raw audio")
        self._clear_queue(self.audio_segment_queue, "audio segment")
        self._clear_queue(self.transcription_queue, "transcription")
        if self.enable_improvement:
            self._clear_queue(self.text_improvement_queue, "text improvement")
            self._clear_queue(self.sentence_queue, "sentence")
            self._clear_queue(self.improved_transcription_queue, "improved transcription")

        # Final consolidation if not already done
        if self.temp_transcriptions or (self.enable_improvement and self.temp_improved_transcriptions):
            self.consolidate_final_output()

        # Cleanup device manager
        self.device_manager.cleanup()

    def _clear_queue(self, q: queue.Queue, name: str):
        """Clear a queue and report the number of items dropped."""
        count = 0
        try:
            while True:
                q.get_nowait()
                count += 1
        except queue.Empty:
            pass


def main():
    """Main function for the real-time speech-to-text CLI."""
    parser = argparse.ArgumentParser(
        description="Real-time Speech-to-Text using OpenAI Whisper",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python realtime_speech_to_text.py
  python realtime_speech_to_text.py --model small --language en
  python realtime_speech_to_text.py --output temp_transcription.txt
  python realtime_speech_to_text.py --model base --language en --output temp.txt
  python realtime_speech_to_text.py --debug-vad --output debug_session.txt
  python realtime_speech_to_text.py --list-devices
  python realtime_speech_to_text.py --enable-improvement --t5-model base
  python realtime_speech_to_text.py --enable-improvement --t5-model small --output session.txt

Features:
  - True parallel processing with up to 5 dedicated threads
  - 1-second intervals OR word boundary detection
  - Real-time console output with timestamps
  - Sentence-by-sentence T5-based text improvement for grammar correction
  - Sentence boundary detection and accumulation for optimal processing
  - Preserves all original transcribed words exactly while enhancing grammar
  - Temporary file output during session
  - Final consolidated output in 'final_transcribed.txt'
  - Sentence-level improved text output in 'improved_transcribed.txt' (when enabled)
  - Enhanced error handling and graceful degradation
        """
    )
    
    parser.add_argument(
        '--model', '-m',
        type=str,
        default='base',
        choices=['tiny', 'base', 'small', 'medium', 'large'],
        help='Whisper model to use (default: base)'
    )
    
    parser.add_argument(
        '--language', '-l',
        type=str,
        help='Language code (e.g., en, es, fr). Auto-detect if not specified'
    )
    
    parser.add_argument(
        '--list-devices',
        action='store_true',
        help='List available audio input devices and exit'
    )

    parser.add_argument(
        '--output', '-o',
        type=str,
        help='Output text file to save transcriptions (optional)'
    )

    parser.add_argument(
        '--debug-vad',
        action='store_true',
        help='Enable Voice Activity Detection debug output'
    )

    parser.add_argument(
        '--t5-model',
        type=str,
        default='base',
        choices=['mini', 'small', 'base'],
        help='T5 model variant for text improvement (default: base)'
    )

    parser.add_argument(
        '--enable-improvement',
        action='store_true',
        help='Enable text improvement using T5 model'
    )

    args = parser.parse_args()
    
    # Initialize device manager for listing devices
    if args.list_devices:
        device_manager = AudioDeviceManager()
        device_manager.list_devices()
        device_manager.cleanup()
        return
    

    
    try:
        # Initialize the speech-to-text converter
        converter = RealTimeSpeechToText(
            model_name=args.model,
            language=args.language,
            output_file=args.output,
            debug_vad=args.debug_vad,
            t5_model=args.t5_model,
            enable_improvement=args.enable_improvement
        )
        
        # Start listening
        success = converter.start_listening()
        
        # Cleanup
        converter.cleanup()
        
        if not success:
            print("❌ Session failed!")
            sys.exit(1)

    except KeyboardInterrupt:
        pass
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()

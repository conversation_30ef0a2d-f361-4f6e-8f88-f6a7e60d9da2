#!/usr/bin/env python3
"""
Build script for creating standalone executables of the Real-time Speech-to-Text application.

This script automates the process of:
1. Installing build dependencies
2. Pre-downloading Whisper models
3. Creating PyInstaller executable
4. Bundling models with the executable

Usage:
    python build_binary.py [options]
"""

import os
import sys
import subprocess
import shutil
import argparse
from pathlib import Path
import whisper


def run_command(command, description=""):
    """Run a shell command and handle errors."""
    print(f"🔄 {description}")
    print(f"   Command: {command}")
    
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        if result.stdout:
            print(f"   Output: {result.stdout.strip()}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error: {e}")
        if e.stdout:
            print(f"   Stdout: {e.stdout}")
        if e.stderr:
            print(f"   Stderr: {e.stderr}")
        return False


def install_build_dependencies():
    """Install PyInstaller and other build dependencies."""
    print("📦 Installing build dependencies...")
    
    dependencies = ["pyinstaller", "auto-py-to-exe"]
    
    for dep in dependencies:
        if not run_command(f"pip install {dep}", f"Installing {dep}"):
            print(f"⚠️  Failed to install {dep}, continuing anyway...")
    
    return True


def download_models(models):
    """Pre-download specified Whisper models."""
    print("📥 Pre-downloading Whisper models...")
    
    models_dir = Path("models")
    models_dir.mkdir(exist_ok=True)
    
    for model_name in models:
        print(f"   Downloading {model_name} model...")
        try:
            model = whisper.load_model(model_name)
            print(f"   ✅ {model_name} model downloaded successfully")
        except Exception as e:
            print(f"   ❌ Failed to download {model_name}: {e}")
    
    # Copy models from cache to local directory
    cache_dir = Path.home() / ".cache" / "whisper"
    if cache_dir.exists():
        print("📁 Copying models to local directory...")
        for model_file in cache_dir.glob("*.pt"):
            dest_file = models_dir / model_file.name
            if not dest_file.exists():
                shutil.copy2(model_file, dest_file)
                print(f"   Copied: {model_file.name}")
    
    return True


def create_pyinstaller_spec(include_models=True, console=True):
    """Create a PyInstaller spec file."""
    spec_content = f'''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# Data files to include
datas = []
'''
    
    if include_models:
        spec_content += '''
# Include Whisper models
import os
models_dir = 'models'
if os.path.exists(models_dir):
    for model_file in os.listdir(models_dir):
        if model_file.endswith('.pt'):
            datas.append((os.path.join(models_dir, model_file), 'models'))
'''

    spec_content += f'''
a = Analysis(
    ['realtime_speech_to_text.py'],
    pathex=[],
    binaries=[],
    datas=datas,
    hiddenimports=[
        'whisper',
        'pyaudio',
        'numpy',
        'torch',
        'torchaudio',
        'tiktoken',
        'numba',
    ],
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='RealTimeSpeechToText',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console={str(console).lower()},
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
'''
    
    spec_file = Path("RealTimeSpeechToText.spec")
    with open(spec_file, 'w') as f:
        f.write(spec_content)
    
    print(f"📝 Created PyInstaller spec file: {spec_file}")
    return spec_file


def build_executable(spec_file, clean=True):
    """Build the executable using PyInstaller."""
    print("🔨 Building executable with PyInstaller...")
    
    # Clean previous builds
    if clean:
        for dir_name in ["build", "dist"]:
            if Path(dir_name).exists():
                shutil.rmtree(dir_name)
                print(f"   Cleaned {dir_name} directory")
    
    # Build the executable
    command = f"pyinstaller {spec_file}"
    if not run_command(command, "Building executable"):
        return False
    
    # Check if build was successful
    exe_name = "RealTimeSpeechToText.exe" if os.name == "nt" else "RealTimeSpeechToText"
    exe_path = Path("dist") / exe_name
    
    if exe_path.exists():
        size_mb = exe_path.stat().st_size / (1024 * 1024)
        print(f"✅ Executable built successfully!")
        print(f"   Location: {exe_path}")
        print(f"   Size: {size_mb:.1f} MB")
        return True
    else:
        print("❌ Executable not found after build")
        return False


def create_distribution_package():
    """Create a distribution package with models and documentation."""
    print("📦 Creating distribution package...")
    
    dist_dir = Path("distribution")
    if dist_dir.exists():
        shutil.rmtree(dist_dir)
    
    dist_dir.mkdir()
    
    # Copy executable
    exe_name = "RealTimeSpeechToText.exe" if os.name == "nt" else "RealTimeSpeechToText"
    exe_path = Path("dist") / exe_name
    
    if exe_path.exists():
        shutil.copy2(exe_path, dist_dir / exe_name)
        print(f"   Copied executable: {exe_name}")
    
    # Copy models directory
    models_dir = Path("models")
    if models_dir.exists():
        shutil.copytree(models_dir, dist_dir / "models")
        print("   Copied models directory")
    
    # Copy documentation
    for doc_file in ["REALTIME_README.md", "README.md"]:
        if Path(doc_file).exists():
            shutil.copy2(doc_file, dist_dir / doc_file)
            print(f"   Copied documentation: {doc_file}")
    
    # Create a simple launcher script
    launcher_content = f'''@echo off
echo Starting Real-time Speech-to-Text...
{exe_name} %*
pause
'''
    
    if os.name == "nt":
        with open(dist_dir / "start.bat", 'w') as f:
            f.write(launcher_content)
        print("   Created start.bat launcher")
    
    print(f"✅ Distribution package created in: {dist_dir}")
    return True


def main():
    """Main build function."""
    parser = argparse.ArgumentParser(
        description="Build standalone executable for Real-time Speech-to-Text",
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    parser.add_argument(
        '--models', '-m',
        nargs='+',
        default=['tiny', 'base', 'small'],
        choices=['tiny', 'base', 'small', 'medium', 'large'],
        help='Whisper models to include (default: tiny, base, small)'
    )
    
    parser.add_argument(
        '--no-models',
        action='store_true',
        help='Do not bundle models with executable'
    )
    
    parser.add_argument(
        '--console',
        action='store_true',
        default=True,
        help='Keep console window (default: True)'
    )
    
    parser.add_argument(
        '--skip-deps',
        action='store_true',
        help='Skip installing build dependencies'
    )
    
    parser.add_argument(
        '--clean',
        action='store_true',
        default=True,
        help='Clean previous builds (default: True)'
    )
    
    args = parser.parse_args()
    
    print("🚀 Real-time Speech-to-Text Build Script")
    print("=" * 50)
    
    try:
        # Install build dependencies
        if not args.skip_deps:
            if not install_build_dependencies():
                print("❌ Failed to install build dependencies")
                return False
        
        # Download models
        if not args.no_models:
            if not download_models(args.models):
                print("❌ Failed to download models")
                return False
        
        # Create PyInstaller spec
        spec_file = create_pyinstaller_spec(
            include_models=not args.no_models,
            console=args.console
        )
        
        # Build executable
        if not build_executable(spec_file, args.clean):
            print("❌ Failed to build executable")
            return False
        
        # Create distribution package
        if not create_distribution_package():
            print("❌ Failed to create distribution package")
            return False
        
        print("\n🎉 Build completed successfully!")
        print("\nNext steps:")
        print("1. Test the executable in the 'distribution' folder")
        print("2. Distribute the entire 'distribution' folder to users")
        print("3. Users can run the executable directly without Python")
        
        return True
        
    except KeyboardInterrupt:
        print("\n🛑 Build cancelled by user")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

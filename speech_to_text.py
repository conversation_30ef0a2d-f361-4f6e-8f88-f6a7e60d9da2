#!/usr/bin/env python3
"""
Speech-to-Text Converter using OpenAI Whisper

This script processes audio files in a directory and converts them to text
using OpenAI's Whisper speech recognition model.

Usage:
    python speech_to_text.py [input_directory] [options]

Example:
    python speech_to_text.py ./audio_files --model medium --output ./transcripts
"""

import os
import sys
import argparse
import whisper
from pathlib import Path
import time
from typing import List, Optional


class SpeechToTextConverter:
    """Speech-to-text converter using OpenAI Whisper."""
    
    # Supported audio file extensions
    SUPPORTED_EXTENSIONS = {'.mp3', '.wav', '.m4a', '.flac', '.ogg', '.wma', '.aac'}
    
    # Available Whisper models (from smallest to largest)
    AVAILABLE_MODELS = ['tiny', 'base', 'small', 'medium', 'large']
    
    def __init__(self, model_name: str = 'base'):
        """
        Initialize the converter with a Whisper model.
        
        Args:
            model_name: Name of the Whisper model to use
        """
        if model_name not in self.AVAILABLE_MODELS:
            raise ValueError(f"Model '{model_name}' not available. Choose from: {self.AVAILABLE_MODELS}")
        
        print(f"Loading Whisper model '{model_name}'...")
        self.model = whisper.load_model(model_name)
        self.model_name = model_name
        print(f"Model '{model_name}' loaded successfully!")
    
    def find_audio_files(self, directory: Path) -> List[Path]:
        """
        Find all supported audio files in the given directory.
        
        Args:
            directory: Path to the directory to search
            
        Returns:
            List of audio file paths
        """
        audio_files = []
        
        for file_path in directory.rglob('*'):
            if file_path.is_file() and file_path.suffix.lower() in self.SUPPORTED_EXTENSIONS:
                audio_files.append(file_path)
        
        return sorted(audio_files)
    
    def transcribe_file(self, audio_path: Path, language: Optional[str] = None) -> dict:
        """
        Transcribe a single audio file.
        
        Args:
            audio_path: Path to the audio file
            language: Optional language code (e.g., 'en', 'es', 'fr')
            
        Returns:
            Dictionary containing transcription results
        """
        print(f"Transcribing: {audio_path.name}")
        
        try:
            # Transcribe the audio file
            result = self.model.transcribe(
                str(audio_path),
                language=language,
                verbose=False
            )
            
            return {
                'success': True,
                'text': result['text'].strip(),
                'language': result.get('language', 'unknown'),
                'segments': result.get('segments', [])
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'text': '',
                'language': 'unknown',
                'segments': []
            }
    
    def save_transcription(self, audio_path: Path, result: dict, output_dir: Path, 
                          include_timestamps: bool = False) -> Path:
        """
        Save transcription to a text file.
        
        Args:
            audio_path: Original audio file path
            result: Transcription result dictionary
            output_dir: Directory to save transcription files
            include_timestamps: Whether to include timestamp information
            
        Returns:
            Path to the saved transcription file
        """
        # Create output filename
        output_filename = audio_path.stem + '_transcript.txt'
        output_path = output_dir / output_filename
        
        # Ensure output directory exists
        output_dir.mkdir(parents=True, exist_ok=True)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(f"Transcription of: {audio_path.name}\n")
            f.write(f"Model used: {self.model_name}\n")
            f.write(f"Language detected: {result.get('language', 'unknown')}\n")
            f.write("-" * 50 + "\n\n")
            
            if result['success']:
                if include_timestamps and result.get('segments'):
                    # Write with timestamps
                    for segment in result['segments']:
                        start_time = segment.get('start', 0)
                        end_time = segment.get('end', 0)
                        text = segment.get('text', '').strip()
                        f.write(f"[{start_time:.2f}s - {end_time:.2f}s] {text}\n")
                else:
                    # Write plain text
                    f.write(result['text'])
            else:
                f.write(f"ERROR: {result.get('error', 'Unknown error occurred')}")
        
        return output_path
    
    def process_directory(self, input_dir: Path, output_dir: Path, 
                         language: Optional[str] = None, 
                         include_timestamps: bool = False) -> dict:
        """
        Process all audio files in a directory.
        
        Args:
            input_dir: Directory containing audio files
            output_dir: Directory to save transcriptions
            language: Optional language code
            include_timestamps: Whether to include timestamps
            
        Returns:
            Dictionary with processing statistics
        """
        if not input_dir.exists():
            raise FileNotFoundError(f"Input directory not found: {input_dir}")
        
        # Find all audio files
        audio_files = self.find_audio_files(input_dir)
        
        if not audio_files:
            print(f"No audio files found in {input_dir}")
            print(f"Supported formats: {', '.join(self.SUPPORTED_EXTENSIONS)}")
            return {'processed': 0, 'successful': 0, 'failed': 0}
        
        print(f"Found {len(audio_files)} audio files to process")
        
        # Process each file
        successful = 0
        failed = 0
        start_time = time.time()
        
        for i, audio_path in enumerate(audio_files, 1):
            print(f"\nProcessing file {i}/{len(audio_files)}: {audio_path.name}")
            
            # Transcribe the file
            result = self.transcribe_file(audio_path, language)
            
            # Save the transcription
            try:
                output_path = self.save_transcription(
                    audio_path, result, output_dir, include_timestamps
                )
                
                if result['success']:
                    successful += 1
                    print(f"✓ Transcription saved to: {output_path}")
                else:
                    failed += 1
                    print(f"✗ Failed to transcribe: {result.get('error', 'Unknown error')}")
                    
            except Exception as e:
                failed += 1
                print(f"✗ Failed to save transcription: {e}")
        
        # Print summary
        total_time = time.time() - start_time
        print(f"\n" + "="*50)
        print(f"Processing complete!")
        print(f"Total files: {len(audio_files)}")
        print(f"Successful: {successful}")
        print(f"Failed: {failed}")
        print(f"Total time: {total_time:.2f} seconds")
        print(f"Average time per file: {total_time/len(audio_files):.2f} seconds")
        
        return {
            'processed': len(audio_files),
            'successful': successful,
            'failed': failed,
            'total_time': total_time
        }


def main():
    """Main function to run the speech-to-text converter."""
    parser = argparse.ArgumentParser(
        description="Convert speech to text using OpenAI Whisper",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python speech_to_text.py ./audio_files
  python speech_to_text.py ./audio_files --model medium --output ./transcripts
  python speech_to_text.py ./audio_files --language en --timestamps
        """
    )
    
    parser.add_argument(
        'input_directory',
        type=str,
        help='Directory containing audio files to transcribe'
    )
    
    parser.add_argument(
        '--output', '-o',
        type=str,
        default='./transcripts',
        help='Output directory for transcription files (default: ./transcripts)'
    )
    
    parser.add_argument(
        '--model', '-m',
        type=str,
        default='base',
        choices=SpeechToTextConverter.AVAILABLE_MODELS,
        help='Whisper model to use (default: base)'
    )
    
    parser.add_argument(
        '--language', '-l',
        type=str,
        help='Language code (e.g., en, es, fr). If not specified, language will be auto-detected'
    )
    
    parser.add_argument(
        '--timestamps', '-t',
        action='store_true',
        help='Include timestamps in the transcription output'
    )
    
    args = parser.parse_args()
    
    try:
        # Initialize converter
        converter = SpeechToTextConverter(args.model)
        
        # Convert paths
        input_dir = Path(args.input_directory)
        output_dir = Path(args.output)
        
        # Process directory
        stats = converter.process_directory(
            input_dir=input_dir,
            output_dir=output_dir,
            language=args.language,
            include_timestamps=args.timestamps
        )
        
        # Exit with appropriate code
        if stats['failed'] > 0:
            sys.exit(1)
        else:
            sys.exit(0)
            
    except KeyboardInterrupt:
        print("\nOperation cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
